import sys
import pandas as pd

from semantic_classifier.SBertClassifier import SBertClassifier  # p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>d<PERSON> se, že třída je ve stejném adresáři nebo v PYTHONPATH

def do(df):

    # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sloupců
    required_cols = {"text", "value_class"}
    if not required_cols.issubset(df.columns):
        raise ValueError(f"Dataset musí obsahovat sloupce: {required_cols}")

    # Výběr textů s value_class == 0
    mask = df["value_class"] == 0
    texts = df.loc[mask, "text"].tolist()

    # Inicializace klasifikátoru
    classifier = SBertClassifier()

    # <PERSON><PERSON>ifika<PERSON> textů s debug informacemi batchově
    results = classifier.classify_batch_debug(texts)
    key_classes = [class_id for class_id, class_name, similarity in results]
    key_class_names = [class_name for class_id, class_name, similarity in results]
    similarities = [similarity for class_id, class_name, similarity in results]

    # <PERSON><PERSON>ž<PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON><PERSON> do dataframe
    df.loc[mask, "key_class"] = key_classes
    df.loc[mask, "key_class_name"] = key_class_names
    df.loc[mask, "similarity"] = similarities
