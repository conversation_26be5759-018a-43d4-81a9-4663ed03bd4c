#!/usr/bin/env python3
"""
Adaptivní trénovací skript pro semantic classifier.
Rozděluje triplety podle síly a používá různé parametry pro každou skupinu.
"""

import os
import random
from pathlib import Path
from torch.utils.data import DataLoader
from sentence_transformers import InputExample, losses, SentenceTransformer
from sentence_transformers.evaluation import TripletEvaluator
from load_training_data_with_scores_xlsx import load_training_data_with_scores_xlsx, recompute_scores_inplace
from generate_triplets import generate_triplets
from SBertClassifier import SBertClassifier
from DataClass import Class
from typing import List, Tuple, Dict

# Konfigurace
DATA_XLSX = Path("training_data/training_set.xlsx")
POS_THRESHOLD = 0.8
NEG_THRESHOLD = 0.65
VALIDATION_SPLIT = 0.2
RANDOM_SEED = 42

# Prahy pro rozdělení tripletů podle síly
STRONG_TRIPLET_THRESHOLD = 0.25  # Silné triplety (>= 0.25)
WEAK_TRIPLET_THRESHOLD = 0.10    # Slabé triplety (< 0.10)
# Střední triplety: 0.10 <= síla < 0.25

# Trénovací parametry pro různé skupiny tripletů
TRAINING_CONFIGS = {
    'strong': {
        'name': 'Silné triplety',
        'epochs': 15,
        'batch_size': 16,  # Menší batch pro přesnější učení
        'learning_rate': 5e-6,  # Konzervativní LR pro jemné doladění
        'weight_decay': 0.02,
        'patience': 3,
        'min_delta': 0.0005,
        'description': 'Konzervativní parametry pro jemné doladění obtížných případů'
    },
    'medium': {
        'name': 'Střední triplety', 
        'epochs': 12,
        'batch_size': 24,  # Střední batch size
        'learning_rate': 1e-5,  # Neutrální LR
        'weight_decay': 0.01,
        'patience': 4,
        'min_delta': 0.001,
        'description': 'Vyvážené parametry pro standardní učení'
    },
    'weak': {
        'name': 'Slabé triplety',
        'epochs': 8,
        'batch_size': 32,  # Větší batch pro stabilnější učení
        'learning_rate': 2e-5,  # Agresivnější LR pro rychlé učení
        'weight_decay': 0.005,
        'patience': 5,
        'min_delta': 0.002,
        'description': 'Agresivní parametry pro rychlé učení jednoduchých případů'
    }
}

class EarlyStopping:
    """Early stopping implementace pro SentenceTransformer trénink."""
    
    def __init__(self, patience=5, min_delta=0.001, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.counter = 0
        self.best_model_state = None
        
    def __call__(self, current_score, model):
        if self.best_score is None:
            self.best_score = current_score
            if self.restore_best_weights:
                self.best_model_state = model.state_dict().copy()
            return False
        
        if current_score > self.best_score + self.min_delta:
            self.best_score = current_score
            self.counter = 0
            if self.restore_best_weights:
                self.best_model_state = model.state_dict().copy()
            print(f"✅ Nové nejlepší skóre: {current_score:.4f}")
            return False
        else:
            self.counter += 1
            print(f"⏳ Žádné zlepšení ({self.counter}/{self.patience}), skóre: {current_score:.4f}")
            
            if self.counter >= self.patience:
                print(f"🛑 Early stopping! Nejlepší skóre: {self.best_score:.4f}")
                if self.restore_best_weights and self.best_model_state:
                    model.load_state_dict(self.best_model_state)
                    print("🔄 Obnoveny nejlepší váhy modelu")
                return True
            return False

def categorize_triplets(triplets: List[Tuple[str, str, str, float]]) -> Dict[str, List[Tuple[str, str, str, float]]]:
    """Rozdělí triplety do kategorií podle síly."""
    strong_triplets = []
    medium_triplets = []
    weak_triplets = []
    
    for triplet in triplets:
        anchor, positive, negative, strength = triplet
        
        if strength >= STRONG_TRIPLET_THRESHOLD:
            strong_triplets.append(triplet)
        elif strength >= WEAK_TRIPLET_THRESHOLD:
            medium_triplets.append(triplet)
        else:
            weak_triplets.append(triplet)
    
    return {
        'strong': strong_triplets,
        'medium': medium_triplets,
        'weak': weak_triplets
    }

def print_triplet_analysis(categorized_triplets: Dict[str, List]):
    """Vypíše analýzu rozdělení tripletů."""
    total = sum(len(triplets) for triplets in categorized_triplets.values())
    
    print(f"\n=== Analýza rozdělení tripletů ===")
    print(f"Celkem tripletů: {total}")
    
    for category, triplets in categorized_triplets.items():
        if triplets:
            strengths = [t[3] for t in triplets]
            config = TRAINING_CONFIGS[category]
            print(f"\n📊 {config['name']}: {len(triplets)} tripletů ({len(triplets)/total*100:.1f}%)")
            print(f"   Síla: {min(strengths):.4f} - {max(strengths):.4f} (avg: {sum(strengths)/len(strengths):.4f})")
            print(f"   Parametry: LR={config['learning_rate']}, batch={config['batch_size']}, epochs={config['epochs']}")
            print(f"   Strategie: {config['description']}")

def train_on_triplet_category(classifier: SBertClassifier, 
                            triplets: List[Tuple[str, str, str, float]], 
                            category: str,
                            validation_triplets: List[Tuple[str, str, str, float]]) -> float:
    """Trénuje model na konkrétní kategorii tripletů."""
    
    if not triplets:
        print(f"⚠️  Žádné triplety pro kategorii {category}, přeskakuji...")
        return 0.0
    
    config = TRAINING_CONFIGS[category]
    print(f"\n🚀 Spouštím trénink pro {config['name']}")
    print(f"📈 Triplety: {len(triplets)}, Parametry: {config}")
    
    # Převod na InputExample
    train_examples = [
        InputExample(texts=[anchor, positive, negative])
        for anchor, positive, negative, _strength in triplets
    ]
    
    # DataLoader s příslušným batch size
    dataloader = DataLoader(train_examples, shuffle=True, batch_size=config['batch_size'])
    train_loss = losses.TripletLoss(model=classifier.model)
    
    # Validační evaluátor
    val_anchors = [anchor for anchor, _, _, _ in validation_triplets]
    val_positives = [positive for _, positive, _, _ in validation_triplets]
    val_negatives = [negative for _, _, negative, _ in validation_triplets]
    
    evaluator = TripletEvaluator(
        anchors=val_anchors,
        positives=val_positives,
        negatives=val_negatives,
        name=f"triplet_evaluation_{category}"
    )
    
    # Early stopping s příslušnými parametry
    early_stopping = EarlyStopping(
        patience=config['patience'],
        min_delta=config['min_delta'],
        restore_best_weights=True
    )
    
    # Warmup steps
    warmup_steps = int(len(dataloader) * config['epochs'] * 0.1)
    
    best_score = 0.0
    
    # Trénovací smyčka
    for epoch in range(config['epochs']):
        print(f"\n🔄 {config['name']} - Epoch {epoch + 1}/{config['epochs']}")
        
        # Trénink jedné epochy
        classifier.model.fit(
            train_objectives=[(dataloader, train_loss)],
            epochs=1,
            warmup_steps=warmup_steps if epoch == 0 else 0,
            show_progress_bar=True,
            use_amp=False,
            optimizer_params={
                'lr': config['learning_rate'], 
                'eps': 1e-8, 
                'weight_decay': config['weight_decay']
            }
        )
        
        # Evaluace
        eval_results = evaluator(classifier.model, output_path=classifier.model_dir)
        current_score = eval_results.get('triplet_evaluation_cosine_accuracy', 0.0)
        
        print(f"📊 {config['name']} Epoch {epoch + 1} - Accuracy: {current_score:.4f}")
        
        # Uložení nejlepšího modelu
        if current_score > best_score:
            best_score = current_score
            classifier.model.save(classifier.model_dir)
            print(f"💾 Model uložen (nové nejlepší skóre: {best_score:.4f})")
        
        # Early stopping
        if early_stopping(current_score, classifier.model):
            print(f"🏁 {config['name']} ukončen po {epoch + 1} epochách")
            break
    
    print(f"✅ {config['name']} dokončen. Nejlepší accuracy: {best_score:.4f}")
    return best_score

def main():
    print("🎯 Adaptivní trénink semantic classifieru")
    print("=" * 60)
    
    # Nastavení random seed
    random.seed(RANDOM_SEED)
    
    # Inicializace klasifikátoru
    classifier = SBertClassifier()
    
    # Načtení dat
    classes = load_training_data_with_scores_xlsx(DATA_XLSX, classifier)
    
    # Generování tripletů
    triplets = generate_triplets(classes, pos_threshold=POS_THRESHOLD, neg_threshold=NEG_THRESHOLD)
    print(f"Celkem generováno {len(triplets)} tripletů")
    
    # Rozdělení na train/validation
    random.shuffle(triplets)
    split_idx = int(len(triplets) * (1 - VALIDATION_SPLIT))
    train_triplets = triplets[:split_idx]
    val_triplets = triplets[split_idx:]
    
    print(f"Trénovací triplety: {len(train_triplets)}")
    print(f"Validační triplety: {len(val_triplets)}")
    
    # Kategorizace trénovacích tripletů
    categorized_triplets = categorize_triplets(train_triplets)
    print_triplet_analysis(categorized_triplets)
    
    # Postupný trénink podle kategorií (od nejslabších po nejsilnější)
    training_order = ['weak', 'medium', 'strong']
    results = {}
    
    for category in training_order:
        if categorized_triplets[category]:
            score = train_on_triplet_category(
                classifier, 
                categorized_triplets[category], 
                category, 
                val_triplets
            )
            results[category] = score
        else:
            results[category] = 0.0
    
    # Finální výsledky
    print(f"\n🏆 === FINÁLNÍ VÝSLEDKY ===")
    for category in training_order:
        config = TRAINING_CONFIGS[category]
        score = results[category]
        print(f"{config['name']}: {score:.4f}")
    
    best_score = max(results.values()) if results.values() else 0.0
    print(f"\n🥇 Nejlepší celkové skóre: {best_score:.4f}")
    
    # Analýza po tréninku
    print(f"\n=== Analýza po tréninku ===")
    recompute_scores_inplace(classes, classifier)
    new_triplets = generate_triplets(classes, pos_threshold=POS_THRESHOLD, neg_threshold=NEG_THRESHOLD)
    
    print(f"Nový počet tripletů: {len(new_triplets)}")
    if new_triplets:
        strengths = [strength for _, _, _, strength in new_triplets]
        print(f"Průměrná síla: {sum(strengths)/len(strengths):.4f}")
        print(f"Min/Max síla: {min(strengths):.4f} / {max(strengths):.4f}")
    
    print(f"\n✅ Adaptivní trénink dokončen. Model uložen do: {classifier.model_dir}")

if __name__ == "__main__":
    main()
