#!/usr/bin/env python3
"""
Skript pro analýzu kvality trénovacích dat před trénováním.
Pomáhá identifikovat problémy v datech a optimalizovat parametry.
"""

import os
from pathlib import Path
import matplotlib.pyplot as plt
import numpy as np
from load_training_data_with_scores_xlsx import load_training_data_with_scores_xlsx
from generate_triplets import generate_triplets
from SBertClassifier import SBertClassifier

# Konfigurace
DATA_XLSX = Path("training_data/training_set.xlsx")
POS_THRESHOLD = 0.8
NEG_THRESHOLD = 0.65

def analyze_class_data(classes):
    """Analyzuje kvalitu dat pro každou třídu."""
    print("=== Analýza tříd ===")
    
    for class_name, cls in classes.items():
        print(f"\n📁 Třída: {class_name}")
        print(f"   Kotva: '{cls.anchor}'")
        print(f"   Pozitivní příklady: {len(cls.positive)}")
        print(f"   Negativní příklady: {len(cls.negative)}")
        
        # Statistiky skóre pro pozitivní příklady
        pos_scores = [score for _, score in cls.positive]
        if pos_scores:
            print(f"   Pozitivní skóre - min: {min(pos_scores):.3f}, max: {max(pos_scores):.3f}, avg: {np.mean(pos_scores):.3f}")
            above_threshold = sum(1 for s in pos_scores if s > POS_THRESHOLD)
            print(f"   Pozitivní nad prahem {POS_THRESHOLD}: {above_threshold}/{len(pos_scores)} ({above_threshold/len(pos_scores)*100:.1f}%)")
        
        # Statistiky skóre pro negativní příklady
        neg_scores = [score for _, score in cls.negative]
        if neg_scores:
            print(f"   Negativní skóre - min: {min(neg_scores):.3f}, max: {max(neg_scores):.3f}, avg: {np.mean(neg_scores):.3f}")
            above_threshold = sum(1 for s in neg_scores if s >= NEG_THRESHOLD)
            print(f"   Negativní nad prahem {NEG_THRESHOLD}: {above_threshold}/{len(neg_scores)} ({above_threshold/len(neg_scores)*100:.1f}%)")

def analyze_triplets(triplets):
    """Analyzuje kvalitu generovaných tripletů."""
    print(f"\n=== Analýza tripletů ===")
    print(f"Celkový počet tripletů: {len(triplets)}")
    
    if not triplets:
        print("⚠️  Žádné triplety nebyly vygenerovány!")
        return
    
    # Statistiky síly tripletů
    strengths = [strength for _, _, _, strength in triplets]
    print(f"Síla tripletů:")
    print(f"  Min: {min(strengths):.4f}")
    print(f"  Max: {max(strengths):.4f}")
    print(f"  Průměr: {np.mean(strengths):.4f}")
    print(f"  Medián: {np.median(strengths):.4f}")
    
    # Distribuce síly
    weak_triplets = sum(1 for s in strengths if s < 0.1)
    medium_triplets = sum(1 for s in strengths if 0.1 <= s < 0.3)
    strong_triplets = sum(1 for s in strengths if s >= 0.3)
    
    print(f"Distribuce síly:")
    print(f"  Slabé (< 0.1): {weak_triplets} ({weak_triplets/len(strengths)*100:.1f}%)")
    print(f"  Střední (0.1-0.3): {medium_triplets} ({medium_triplets/len(strengths)*100:.1f}%)")
    print(f"  Silné (>= 0.3): {strong_triplets} ({strong_triplets/len(strengths)*100:.1f}%)")

def plot_score_distributions(classes):
    """Vytvoří grafy distribuce skóre."""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Distribuce skóre podobnosti', fontsize=16)
    
    all_pos_scores = []
    all_neg_scores = []
    
    for cls in classes.values():
        pos_scores = [score for _, score in cls.positive]
        neg_scores = [score for _, score in cls.negative]
        all_pos_scores.extend(pos_scores)
        all_neg_scores.extend(neg_scores)
    
    # Histogram pozitivních skóre
    axes[0,0].hist(all_pos_scores, bins=30, alpha=0.7, color='green', edgecolor='black')
    axes[0,0].axvline(POS_THRESHOLD, color='red', linestyle='--', label=f'Práh {POS_THRESHOLD}')
    axes[0,0].set_title('Pozitivní příklady')
    axes[0,0].set_xlabel('Skóre podobnosti')
    axes[0,0].set_ylabel('Počet')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # Histogram negativních skóre
    axes[0,1].hist(all_neg_scores, bins=30, alpha=0.7, color='red', edgecolor='black')
    axes[0,1].axvline(NEG_THRESHOLD, color='green', linestyle='--', label=f'Práh {NEG_THRESHOLD}')
    axes[0,1].set_title('Negativní příklady')
    axes[0,1].set_xlabel('Skóre podobnosti')
    axes[0,1].set_ylabel('Počet')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # Box plot porovnání
    axes[1,0].boxplot([all_pos_scores, all_neg_scores], labels=['Pozitivní', 'Negativní'])
    axes[1,0].set_title('Porovnání distribucí')
    axes[1,0].set_ylabel('Skóre podobnosti')
    axes[1,0].grid(True, alpha=0.3)
    
    # Scatter plot pozitivní vs negativní
    class_pos_means = []
    class_neg_means = []
    
    for cls in classes.values():
        if cls.positive and cls.negative:
            pos_mean = np.mean([score for _, score in cls.positive])
            neg_mean = np.mean([score for _, score in cls.negative])
            class_pos_means.append(pos_mean)
            class_neg_means.append(neg_mean)
    
    axes[1,1].scatter(class_pos_means, class_neg_means, alpha=0.7)
    axes[1,1].plot([0, 1], [0, 1], 'r--', alpha=0.5, label='y=x')
    axes[1,1].set_xlabel('Průměrné pozitivní skóre')
    axes[1,1].set_ylabel('Průměrné negativní skóre')
    axes[1,1].set_title('Pozitivní vs Negativní skóre po třídách')
    axes[1,1].legend()
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('training_data_analysis.png', dpi=300, bbox_inches='tight')
    print(f"\n📊 Graf uložen jako 'training_data_analysis.png'")

def suggest_thresholds(classes):
    """Navrhne optimální prahy na základě dat."""
    print(f"\n=== Návrhy optimalizace ===")
    
    all_pos_scores = []
    all_neg_scores = []
    
    for cls in classes.values():
        pos_scores = [score for _, score in cls.positive]
        neg_scores = [score for _, score in cls.negative]
        all_pos_scores.extend(pos_scores)
        all_neg_scores.extend(neg_scores)
    
    if all_pos_scores and all_neg_scores:
        pos_median = np.median(all_pos_scores)
        neg_median = np.median(all_neg_scores)
        
        # Navrhni prahy na základě percentilů
        pos_25th = np.percentile(all_pos_scores, 25)
        neg_75th = np.percentile(all_neg_scores, 75)
        
        print(f"Současné prahy: POS_THRESHOLD={POS_THRESHOLD}, NEG_THRESHOLD={NEG_THRESHOLD}")
        print(f"Navrhované prahy na základě dat:")
        print(f"  POS_THRESHOLD={pos_25th:.3f} (25. percentil pozitivních)")
        print(f"  NEG_THRESHOLD={neg_75th:.3f} (75. percentil negativních)")
        print(f"Mediány: pozitivní={pos_median:.3f}, negativní={neg_median:.3f}")

def main():
    print("🔍 Analýza trénovacích dat")
    print("=" * 50)
    
    # Inicializace klasifikátoru
    classifier = SBertClassifier()
    
    # Načtení dat
    classes = load_training_data_with_scores_xlsx(DATA_XLSX, classifier)
    
    # Analýzy
    analyze_class_data(classes)
    
    # Generování tripletů
    triplets = generate_triplets(classes, pos_threshold=POS_THRESHOLD, neg_threshold=NEG_THRESHOLD)
    analyze_triplets(triplets)
    
    # Návrhy optimalizace
    suggest_thresholds(classes)
    
    # Grafy (pokud je matplotlib dostupný)
    try:
        plot_score_distributions(classes)
    except ImportError:
        print("⚠️  Matplotlib není dostupný - grafy nebudou vytvořeny")
    except Exception as e:
        print(f"⚠️  Chyba při vytváření grafů: {e}")
    
    print("\n✅ Analýza dokončena")

if __name__ == "__main__":
    main()
