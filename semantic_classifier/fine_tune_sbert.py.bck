#!/usr/bin/env python3
"""
Fine-tuning skript pro SBertClassifier.

Načte tréninková data pomocí data_util.generate_triplet_dataset a provede
fine-tuning sentence transformer modelu pomocí triplet loss.
Nezasahuje do třídy SBertClassifier - vytváří nový v<PERSON>ý model.
"""

import os
import sys
import torch
import numpy as np
from datetime import datetime

from torch.utils.data import DataLoader
from sentence_transformers import SentenceTransformer, InputExample, losses, models
from sentence_transformers.evaluation import TripletEvaluator, BinaryClassificationEvaluator
import pandas as pd

# Přidáme cestu k projektu
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from semantic_classifier.data_util import generate_triplet_dataset, generate_smart_triplet_dataset


class SBertFineTuner:
    """
    Třída pro fine-tuning sentence transformer modelů pomocí triplet loss.
    """

    def __init__(self, base_model_name="sentence-transformers/paraphrase-multilingual-mpnet-base-v2",
                 device="cpu"):
        """
        Inicializuje fine-tuner.

        Args:
            base_model_name: název základního modelu
            device: zařízení pro trénování ('cpu', 'cuda', 'mps')
        """
        self.base_model_name = base_model_name
        self.device = device
        self.model = None
        self.triplet_data = None

        print(f"SBertFineTuner inicializován (device: {device})")

    def load_data(self, xlsx_path):
        """
        Načte tréninková data z Excel souboru pomocí generate_triplet_dataset.

        Args:
            xlsx_path: cesta k Excel souboru s tréninkovými daty
        """
        print(f"Načítám tréninková data z: {xlsx_path}")

        if not os.path.exists(xlsx_path):
            raise FileNotFoundError(f"Soubor nenalezen: {xlsx_path}")

        # Načteme triplet data
        self.triplet_data = generate_smart_triplet_dataset(xlsx_path)

        print(f"✅ Načteno {len(self.triplet_data)} tripletů")
        print(f"📊 Sloupce: {list(self.triplet_data.columns)}")

        # Zobrazíme ukázku dat
        print("\n📝 Ukázka dat:")
        for i in range(min(3, len(self.triplet_data))):
            row = self.triplet_data.iloc[i]
            print(f"  {i+1}. Anchor: '{row['anchor'][:50]}...'")
            print(f"     Positive: '{row['positive']}'")
            print(f"     Negative: '{row['negative']}'")
            print()

    def prepare_model(self):
        word_embedding_model = models.Transformer(self.base_model_name)
        pooling_model = models.Pooling(
            word_embedding_model.get_word_embedding_dimension(),
            pooling_mode_mean_tokens=True
        )
        normalize_layer = models.Normalize()  # KRITICKÉ
        self.model = SentenceTransformer(
            modules=[word_embedding_model, pooling_model, normalize_layer],
            device=self.device
        )

    def prepare_evaluator(self, examples, name='val'):
        sentences1 = []
        sentences2 = []
        labels = []

        for ex in examples:
            # Anchor-Positive (similar)
            sentences1.append(ex.texts[0])
            sentences2.append(ex.texts[1])
            labels.append(1.0)  # Similar

            # Anchor-Negative (dissimilar)
            sentences1.append(ex.texts[0])
            sentences2.append(ex.texts[2])
            labels.append(0.0)  # Dissimilar

        return BinaryClassificationEvaluator(sentences1, sentences2, labels, name=name)

    def prepare_training_data(self, train_ratio=0.8):
        """
        Připraví tréninková a validační data.

        Args:
            train_ratio: poměr trénovacích dat (0.8 = 80% train, 20% validation)

        Returns:
            tuple: (train_examples, val_examples)
        """
        if self.triplet_data is None:
            raise RuntimeError("Data nejsou načtena. Spusťte load_data() nejprve.")

        print(f"Připravuji tréninková data (train ratio: {train_ratio})")

        # Rozdělíme data na train/validation
        n_total = len(self.triplet_data)
        n_train = int(n_total * train_ratio)

        # Zamícháme data
        shuffled_data = self.triplet_data.sample(frac=1, random_state=42).reset_index(drop=True)

        train_data = shuffled_data[:n_train]
        val_data = shuffled_data[n_train:]

        # Vytvoříme InputExample objekty pro trénování
        train_examples = []
        for _, row in train_data.iterrows():
            example = InputExample(
                texts=[row['anchor'], row['positive'], row['negative']]
            )
            train_examples.append(example)

        # Vytvoříme InputExample objekty pro validaci
        val_examples = []
        for _, row in val_data.iterrows():
            example = InputExample(
                texts=[row['anchor'], row['positive'], row['negative']]
            )
            val_examples.append(example)

        print(f"✅ Připraveno {len(train_examples)} trénovacích a {len(val_examples)} validačních příkladů")

        return train_examples, val_examples

    def fine_tune(self, xlsx_path, output_dir="./fine_tuned_sbert",
                  epochs=5, batch_size=64, warmup_steps=100,
                  train_ratio=0.8, evaluation_steps=10):
        """
        Provede fine-tuning modelu.

        Args:
            xlsx_path: cesta k tréninkovým datům
            output_dir: složka pro uložení vyladěného modelu
            epochs: počet epoch
            batch_size: velikost batch
            warmup_steps: počet warmup kroků
            train_ratio: poměr trénovacích dat
            evaluation_steps: jak často evaluovat
        """
        print("=" * 80)
        print("FINE-TUNING SENTENCE TRANSFORMER")
        print("=" * 80)

        # 1. Načteme data
        self.load_data(xlsx_path)

        # 2. Připravíme model
        self.prepare_model()

        # 3. Připravíme tréninková data
        train_examples, val_examples = self.prepare_training_data(train_ratio)

        # 4. Vytvoříme DataLoader
        train_dataloader = DataLoader(train_examples, shuffle=True, batch_size=batch_size)

        # 5. Definujeme loss funkci
        train_loss = losses.TripletLoss(model=self.model)

        # 6. Vytvoření evaluatoru
        val_evaluator = self.prepare_evaluator(val_examples)

        # Triplet loss s kosínovou vzdáleností
        train_loss = losses.TripletLoss(
            model=self.model,
            distance_metric=losses.TripletDistanceMetric.COSINE,
            triplet_margin=0.3
        )


        # 7. Nastavíme parametry trénování
        total_steps = len(train_dataloader) * epochs

        print(f"\n📋 Parametry trénování:")
        print(f"  Epochs: {epochs}")
        print(f"  Batch size: {batch_size}")
        print(f"  Total steps: {total_steps}")
        print(f"  Warmup steps: {warmup_steps}")
        print(f"  Evaluation steps: {evaluation_steps}")
        print(f"  Output dir: {output_dir}")

        # 8. Spustíme fine-tuning
        print(f"\n🚀 Spouštím fine-tuning...")

        self.model.fit(
            train_objectives=[(train_dataloader, train_loss)],
            evaluator=val_evaluator,
            epochs=epochs,
            evaluation_steps=evaluation_steps,
            warmup_steps=warmup_steps,
            output_path=output_dir,
            optimizer_params={'lr': 2e-5, 'eps': 1e-8},
            weight_decay=0.01,
            scheduler='warmupLinear',
            show_progress_bar=True
        )

        print(f"\n✅ Fine-tuning dokončen!")
        print(f"📁 Vyladěný model uložen v: {output_dir}")

        return output_dir

    def evaluate_model(self, model_path, test_data_path=None):
        """
        Evaluuje vyladěný model.

        Args:
            model_path: cesta k vyladěnému modelu
            test_data_path: cesta k testovacím datům (volitelné)
        """
        print(f"\n📊 Evaluace modelu: {model_path}")

        # Načteme vyladěný model
        tuned_model = SentenceTransformer(model_path, device=self.device)

        # Pokud máme testovací data, použijeme je
        if test_data_path and os.path.exists(test_data_path):
            test_data = generate_triplet_dataset(test_data_path)
            print(f"Testovací data: {len(test_data)} tripletů")

            # Vytvoříme test examples
            test_examples = []
            for _, row in test_data.iterrows():
                example = InputExample(
                    texts=[row['anchor'], row['positive'], row['negative']]
                )
                test_examples.append(example)

            # Evaluace
            evaluator = TripletEvaluator.from_input_examples(
                test_examples,
                name='test'
            )

            score = evaluator(tuned_model)
            print(f"📈 Test score: {score}")

        else:
            print("ℹ️  Žádná testovací data k dispozici")

        # Porovnání s původním modelem
        print(f"\n🔄 Porovnání s původním modelem...")
        original_model = SentenceTransformer(self.base_model_name, device=self.device)

        # Test na několika příkladech
        test_texts = [
            "číslo faktury",
            "datum vystavení",
            "celková částka",
            "dodavatel"
        ]

        print("Porovnání embeddingů:")
        for text in test_texts:
            orig_emb = original_model.encode([text])[0]
            tuned_emb = tuned_model.encode([text])[0]

            # Kosinusová podobnost
            similarity = np.dot(orig_emb, tuned_emb) / (np.linalg.norm(orig_emb) * np.linalg.norm(tuned_emb))

            print(f"  '{text}': podobnost {similarity:.3f}")

def main():
    """Hlavní funkce pro spuštění fine-tuningu."""
    import argparse

    parser = argparse.ArgumentParser(description="Fine-tuning SentenceTransformer modelu")
    parser.add_argument("--data", default="training_data/training_set.xlsx",
                       help="Cesta k tréninkovým datům (Excel)")
    parser.add_argument("--output", default="./fine_tuned_sbert",
                       help="Výstupní složka pro vyladěný model")
    parser.add_argument("--epochs", type=int, default=5,
                       help="Počet epoch (default: 10)")
    parser.add_argument("--batch-size", type=int, default=128,
                       help="Velikost batch (default: 16)")
    parser.add_argument("--device", default="cpu",
                       help="Zařízení (cpu/cuda/mps, default: cpu)")
    parser.add_argument("--evaluate-only",
                       help="Pouze evaluace existujícího modelu (cesta k modelu)")

    args = parser.parse_args()

    try:
        fine_tuner = SBertFineTuner()

        if args.evaluate_only:
            # Pouze evaluace
            fine_tuner.evaluate_model(args.evaluate_only, args.data)
        else:
            # Fine-tuning
            output_dir = fine_tuner.fine_tune(
                xlsx_path="training_data/training_set.xlsx",
                output_dir=args.output,
                epochs=args.epochs,
                batch_size=args.batch_size
            )

            # Automatická evaluace
            fine_tuner.evaluate_model(output_dir, args.data)

    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
