#!/usr/bin/env python3
import os
import random
from pathlib import Path
from torch.utils.data import DataLoader
from sentence_transformers import InputExample, losses, SentenceTransformer
from sentence_transformers.evaluation import TripletEvaluator
from load_training_data_with_scores_xlsx import load_training_data_with_scores_xlsx, recompute_scores_inplace
from generate_triplets import generate_triplets
from SBertClassifier import SBertClassifier
from DataClass import Class

# Konfigurace
DATA_XLSX = Path("training_data/training_set.xlsx")
EPOCHS = 10
BATCH_SIZE = 32
POS_THRESHOLD = 0.8
NEG_THRESHOLD = 0.65
VALIDATION_SPLIT = 0.2  # 20% dat pro validaci
EVALUATION_STEPS = 5   # Evaluace každých 50 kroků
RANDOM_SEED = 42

# Nastavení random seed pro reprodukovatelnost
random.seed(RANDOM_SEED)

# Inicializace klasifikátoru
classifier = SBertClassifier()

# Načtení dat a výpočet skóre
classes: dict[str, Class] = load_training_data_with_scores_xlsx(DATA_XLSX, classifier)

# Generování tripletů podle prahů
triplets = generate_triplets(classes, pos_threshold=POS_THRESHOLD, neg_threshold=NEG_THRESHOLD)
print(f"Počet generovaných tripletů: {len(triplets)}")

# Rozdělení na trénovací a validační data
random.shuffle(triplets)
split_idx = int(len(triplets) * (1 - VALIDATION_SPLIT))
train_triplets = triplets[:split_idx]
val_triplets = triplets[split_idx:]

print(f"Trénovací triplety: {len(train_triplets)}")
print(f"Validační triplety: {len(val_triplets)}")

# Převod tripletů do InputExample pro SentenceTransformer
train_examples = [
    InputExample(texts=[anchor, positive, negative])
    for anchor, positive, negative, _strength in train_triplets
]

# Vytvoření validačních dat pro TripletEvaluator
val_anchors = [anchor for anchor, _, _, _ in val_triplets]
val_positives = [positive for _, positive, _, _ in val_triplets]
val_negatives = [negative for _, _, negative, _ in val_triplets]

# Vytvoření DataLoader a loss funkce
dataloader = DataLoader(train_examples, shuffle=True, batch_size=BATCH_SIZE)
train_loss = losses.TripletLoss(
    model=classifier.model,
    distance_metric=losses.TripletDistanceMetric.COSINE,
    triplet_margin=0.3  # Experimentujte s hodnotou 0.2-0.5
)

# Vytvoření evaluátoru pro sledování pokroku
evaluator = TripletEvaluator(
    anchors=val_anchors,
    positives=val_positives,
    negatives=val_negatives,
    name="triplet_evaluation"
)

warmup_steps = int(len(dataloader) * EPOCHS * 0.1)
print(f"Warmup kroků: {warmup_steps}")
print(f"Evaluace každých {EVALUATION_STEPS} kroků")

# Trénink s evaluátorem
print("Spouštím trénink...")
classifier.model.fit(
    train_objectives=[(dataloader, train_loss)],
    evaluator=evaluator,
    epochs=EPOCHS,
    evaluation_steps=EVALUATION_STEPS,
    warmup_steps=warmup_steps,
    output_path=classifier.model_dir,
    save_best_model=True,
    show_progress_bar=True,
    use_amp=False,
    optimizer_params={'lr': 2e-5, 'eps': 1e-8, 'weight_decay': 0.01},
    weight_decay=0.01,
    scheduler='warmupLinear'
)

# Finální evaluace na validačních datech
print("\n=== Finální evaluace ===")
final_score = evaluator(classifier.model, output_path=classifier.model_dir)
print(f"Finální triplet accuracy: {final_score:.4f}")

# Přepočítání skóre s natrénovaným modelem
print("\n=== Přepočítávám skóre s natrénovaným modelem ===")
recompute_scores_inplace(classes, classifier)

# Analýza zlepšení
print("\n=== Analýza kvality tripletů po tréninku ===")
new_triplets = generate_triplets(classes, pos_threshold=POS_THRESHOLD, neg_threshold=NEG_THRESHOLD)
print(f"Nový počet tripletů: {len(new_triplets)}")

# Statistiky síly tripletů
strengths = [strength for _, _, _, strength in new_triplets]
if strengths:
    print(f"Průměrná síla tripletů: {sum(strengths)/len(strengths):.4f}")
    print(f"Min/Max síla: {min(strengths):.4f} / {max(strengths):.4f}")

print(f"\nTrénink dokončen. Nejlepší model uložen do: {classifier.model_dir}")
