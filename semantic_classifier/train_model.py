#!/usr/bin/env python3
import os
from pathlib import Path
from torch.utils.data import DataLoader
from sentence_transformers import InputExample, losses, SentenceTransformer
from load_training_data_with_scores_xlsx import load_training_data_with_scores_xlsx
from generate_triplets import generate_triplets
from SBertClassifier import SBertClassifier
from DataClass import Class

#DATA_XLSX = "training_set.xlsx"
DATA_XLSX = Path("training_data/training_set.xlsx")
#OUTPUT_MODEL = "output_model_triplet"
EPOCHS = 3
BATCH_SIZE = 64
POS_THRESHOLD = 0.8
NEG_THRESHOLD = 0.65

# Inicializace klasifikátoru
classifier = SBertClassifier()

# Načtení dat a výpočet skóre
classes: dict[str, Class]  = load_training_data_with_scores_xlsx(DATA_XLSX, classifier)

# Generování tripletů podle prahů
triplets = generate_triplets(classes, pos_threshold=POS_THRESHOLD, neg_threshold=NEG_THRESHOLD)
print(f"Počet generovaných tripletů: {len(triplets)}")

# Převod tripletů do InputExample pro SentenceTransformer
train_examples = [
    InputExample(texts=[anchor, positive, negative])
    for anchor, positive, negative, _strength in triplets
]

dataloader = DataLoader(train_examples, shuffle=True, batch_size=BATCH_SIZE)
train_loss = losses.TripletLoss(model=classifier.model)

warmup_steps = int(len(dataloader) * EPOCHS * 0.1)

# Trénink
classifier.model.fit(
    train_objectives=[(dataloader, train_loss)],
    epochs=EPOCHS,
    warmup_steps=warmup_steps,
    #evaluation_steps=10,
    show_progress_bar=True,
    save_best_model=False,  # Vypnuto, protože nemáme evaluátor
    use_amp=False,
    optimizer_params={'lr': 2e-5, 'eps': 1e-8, 'weight_decay': 0.01}
)

# Explicitní uložení natrénovaného modelu
print("Ukládám natrénovaný model...")
classifier.model.save(classifier.model_dir)
print(f"Model uložen do: {classifier.model_dir}")

# Vytvoření prototypů
#classifier.create_and_save_prototypes(classes.keys(), classes.values())

print("Trénink dokončen. Model uložen.")
