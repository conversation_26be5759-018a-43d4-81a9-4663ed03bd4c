#!/usr/bin/env python3
import os
import random
from pathlib import Path
from torch.utils.data import DataLoader
from sentence_transformers import InputExample, losses, SentenceTransformer
from sentence_transformers.evaluation import TripletEvaluator
from load_training_data_with_scores_xlsx import load_training_data_with_scores_xlsx, recompute_scores_inplace
from generate_triplets import generate_triplets
from SBertClassifier import SBertClassifier
from DataClass import Class

# Konfigurace
DATA_XLSX = Path("training_data/training_set.xlsx")
EPOCHS = 20  # Zvýšeno pro early stopping
BATCH_SIZE = 32
POS_THRESHOLD = 0.8
NEG_THRESHOLD = 0.65
VALIDATION_SPLIT = 0.2  # 20% dat pro validaci
EVALUATION_STEPS = 5    # Častější evaluace pro early stopping
RANDOM_SEED = 42

# Early stopping parametry
EARLY_STOPPING_PATIENCE = 5  # Počet evaluací bez zlepšení
EARLY_STOPPING_MIN_DELTA = 0.001  # Minimální zlepšení

# Nastavení random seed pro reprodukovatelnost
random.seed(RANDOM_SEED)

class EarlyStopping:
    """Early stopping implementace pro SentenceTransformer trénink."""

    def __init__(self, patience=5, min_delta=0.001, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.counter = 0
        self.best_model_state = None

    def __call__(self, current_score, model):
        """
        Zkontroluje, zda má trénink pokračovat.

        Args:
            current_score: aktuální skóre (vyšší = lepší)
            model: model pro uložení nejlepšího stavu

        Returns:
            True pokud má trénink skončit, False pokud má pokračovat
        """
        if self.best_score is None:
            self.best_score = current_score
            if self.restore_best_weights:
                self.best_model_state = model.state_dict().copy()
            return False

        # Zlepšení musí být větší než min_delta
        if current_score > self.best_score + self.min_delta:
            self.best_score = current_score
            self.counter = 0
            if self.restore_best_weights:
                self.best_model_state = model.state_dict().copy()
            print(f"✅ Nové nejlepší skóre: {current_score:.4f}")
            return False
        else:
            self.counter += 1
            print(f"⏳ Žádné zlepšení ({self.counter}/{self.patience}), skóre: {current_score:.4f}")

            if self.counter >= self.patience:
                print(f"🛑 Early stopping! Nejlepší skóre: {self.best_score:.4f}")
                if self.restore_best_weights and self.best_model_state:
                    model.load_state_dict(self.best_model_state)
                    print("🔄 Obnoveny nejlepší váhy modelu")
                return True
            return False

# Inicializace klasifikátoru
classifier = SBertClassifier()

# Načtení dat a výpočet skóre
classes: dict[str, Class] = load_training_data_with_scores_xlsx(DATA_XLSX, classifier)

# Generování tripletů podle prahů
triplets = generate_triplets(classes, pos_threshold=POS_THRESHOLD, neg_threshold=NEG_THRESHOLD)
print(f"Počet generovaných tripletů: {len(triplets)}")

# Rozdělení na trénovací a validační data
random.shuffle(triplets)
split_idx = int(len(triplets) * (1 - VALIDATION_SPLIT))
train_triplets = triplets[:split_idx]
val_triplets = triplets[split_idx:]

print(f"Trénovací triplety: {len(train_triplets)}")
print(f"Validační triplety: {len(val_triplets)}")

# Převod tripletů do InputExample pro SentenceTransformer
train_examples = [
    InputExample(texts=[anchor, positive, negative])
    for anchor, positive, negative, _strength in train_triplets
]

# Vytvoření validačních dat pro TripletEvaluator
val_anchors = [anchor for anchor, _, _, _ in val_triplets]
val_positives = [positive for _, positive, _, _ in val_triplets]
val_negatives = [negative for _, _, negative, _ in val_triplets]

# Vytvoření DataLoader a loss funkce
dataloader = DataLoader(train_examples, shuffle=True, batch_size=BATCH_SIZE)
train_loss = losses.TripletLoss(
    model=classifier.model,
    distance_metric=losses.TripletDistanceMetric.COSINE,
    triplet_margin=0.3  # Experimentujte s hodnotou 0.2-0.5
)

# Vytvoření evaluátoru pro sledování pokroku
evaluator = TripletEvaluator(
    anchors=val_anchors,
    positives=val_positives,
    negatives=val_negatives,
    name="triplet_evaluation"
)

warmup_steps = int(len(dataloader) * EPOCHS * 0.1)
print(f"Warmup kroků: {warmup_steps}")
print(f"Evaluace každých {EVALUATION_STEPS} kroků")

# Inicializace early stopping
early_stopping = EarlyStopping(
    patience=EARLY_STOPPING_PATIENCE,
    min_delta=EARLY_STOPPING_MIN_DELTA,
    restore_best_weights=True
)

# Vlastní trénovací smyčka s early stopping
print("Spouštím trénink s early stopping...")
print(f"Early stopping: patience={EARLY_STOPPING_PATIENCE}, min_delta={EARLY_STOPPING_MIN_DELTA}")

best_score = 0.0

for epoch in range(EPOCHS):
    print(f"\n🔄 Epoch {epoch + 1}/{EPOCHS}")

    # Trénink jedné epochy
    classifier.model.fit(
        train_objectives=[(dataloader, train_loss)],
        epochs=1,  # Pouze jedna epocha
        warmup_steps=warmup_steps if epoch == 0 else 0,  # Warmup pouze v první epoše
        show_progress_bar=True,
        use_amp=False,
        optimizer_params={'lr': 1e-5, 'eps': 1e-8, 'weight_decay': 0.01}
    )

    # Evaluace po každé epoše
    eval_results = evaluator(classifier.model, output_path=classifier.model_dir)
    current_score = eval_results.get('triplet_evaluation_cosine_accuracy', 0.0)

    print(f"📊 Epoch {epoch + 1} - Triplet accuracy: {current_score:.4f}")

    # Uložení nejlepšího modelu
    if current_score > best_score:
        best_score = current_score
        classifier.model.save(classifier.model_dir)
        print(f"💾 Model uložen (nové nejlepší skóre: {best_score:.4f})")

    # Kontrola early stopping
    if early_stopping(current_score, classifier.model):
        print(f"🏁 Trénink ukončen po {epoch + 1} epochách")
        break

print(f"\n✅ Trénink dokončen. Nejlepší accuracy: {best_score:.4f}")

# Finální evaluace již proběhla během tréninku
print(f"\n=== Finální výsledky ===")
print(f"Nejlepší triplet accuracy: {best_score:.4f}")

# Přepočítání skóre s natrénovaným modelem
print("\n=== Přepočítávám skóre s natrénovaným modelem ===")
recompute_scores_inplace(classes, classifier)

# Analýza zlepšení
print("\n=== Analýza kvality tripletů po tréninku ===")
new_triplets = generate_triplets(classes, pos_threshold=POS_THRESHOLD, neg_threshold=NEG_THRESHOLD)
print(f"Nový počet tripletů: {len(new_triplets)}")

# Statistiky síly tripletů
strengths = [strength for _, _, _, strength in new_triplets]
if strengths:
    print(f"Průměrná síla tripletů: {sum(strengths)/len(strengths):.4f}")
    print(f"Min/Max síla: {min(strengths):.4f} / {max(strengths):.4f}")

print(f"\nTrénink dokončen. Nejlepší model uložen do: {classifier.model_dir}")
