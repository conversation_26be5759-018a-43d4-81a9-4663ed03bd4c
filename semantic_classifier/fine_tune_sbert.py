#!/usr/bin/env python3
"""
Fine-tuning skript pro SBertClassifier.

Skript pro IDE - všechny parametry se nastavují pomocí proměnných.
Používá existující třídu SBertClassifier pro trénink.
"""

import os
import sys
import torch
import numpy as np
import json
from datetime import datetime

from torch.utils.data import DataLoader
from sentence_transformers import SentenceTransformer, InputExample, losses, models
from sentence_transformers.evaluation import TripletEvaluator, BinaryClassificationEvaluator
import pandas as pd

# Přidáme cestu k projektu
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from semantic_classifier.data_util import generate_triplet_dataset, generate_smart_triplet_dataset, generate_adaptive_triplet_dataset
from semantic_classifier.training_data_preparation import load_training_data_from_xlsx
from semantic_classifier.SBertClassifier import SBertClassifier


class EarlyStoppingTripletEvaluator(TripletEvaluator):
    """
    Zjednodušený TripletEvaluator s early stopping funkcionalitou.
    Sleduje zlepšování a zastaví trénink při stagnaci.
    """

    def __init__(self, anchors, positives, negatives, name='', patience=5, min_delta=0.001):
        super().__init__(anchors, positives, negatives, name=name)
        self.patience = patience
        self.min_delta = min_delta
        self.best_score = -1
        self.patience_counter = 0
        self.should_stop = False

    def __call__(self, model, output_path=None, epoch=-1, steps=-1):
        """Evaluuje model a kontroluje early stopping podmínky."""
        metrics = super().__call__(model, output_path, epoch, steps)

        # TripletEvaluator vrací slovník s metrikami
        if isinstance(metrics, dict):
            # Hledáme hlavní metriku - pro TripletEvaluator je to 'val_cosine_accuracy'
            current_score = metrics.get('val_cosine_accuracy',
                           metrics.get(f'eval_{self.name}_cosine_accuracy',
                           metrics.get('eval_cosine_accuracy',
                           metrics.get('accuracy_cosine',
                           metrics.get('cosine_accuracy',
                           metrics.get('accuracy', 0.0))))))
        else:
            current_score = float(metrics) if metrics is not None else 0.0

        # Kontrola zlepšení
        if current_score > self.best_score + self.min_delta:
            improvement = current_score - self.best_score if self.best_score > 0 else current_score
            self.best_score = current_score
            self.patience_counter = 0
            print(f"✅ Nové nejlepší skóre: {current_score:.4f} (zlepšení o {improvement:.4f})")
        else:
            self.patience_counter += 1
            print(f"⏳ Žádné zlepšení ({self.patience_counter}/{self.patience}), skóre: {current_score:.4f} (nejlepší: {self.best_score:.4f})")

            if self.patience_counter >= self.patience:
                self.should_stop = True
                print(f"🛑 Early stopping aktivován! Nejlepší skóre: {self.best_score:.4f}")

        # Dodatečná kontrola - pokud je skóre příliš nízké po několika epochách
        if current_score < 0.5 and self.patience_counter >= 2:
            print(f"⚠️ Nízké skóre ({current_score:.4f}) - možná špatná konvergence")

        # Pokud je skóre velmi vysoké, můžeme být méně přísní
        if current_score > 0.9:
            print(f"🎯 Vysoké skóre dosaženo: {current_score:.4f}")

        return metrics

class EarlyStoppingBinaryEvaluator(BinaryClassificationEvaluator):
    """
    BinaryClassificationEvaluator s early stopping.
    Sleduje 'val_cosine_accuracy' a zastaví trénink při stagnaci.
    """
    def __init__(self, sentences1, sentences2, labels, name='', patience=5, min_delta=0.001):
        super().__init__(sentences1, sentences2, labels, name=name)
        self.patience = patience
        self.min_delta = min_delta
        self.best_score = -1.0
        self.patience_counter = 0
        self.should_stop = False

    def __call__(self, model, output_path=None, epoch: int = -1, steps: int = -1):
        metrics = super().__call__(model, output_path, epoch, steps)
        # BinaryClassificationEvaluator vrací dict s klíči jako 'val_cosine_accuracy'
        if isinstance(metrics, dict):
            score = metrics.get('val_cosine_accuracy',
                     metrics.get('cosine_accuracy',
                     metrics.get('accuracy', 0.0)))
        else:
            score = float(metrics) if metrics is not None else 0.0

        if score > self.best_score + self.min_delta:
            improvement = score - self.best_score if self.best_score >= 0 else score
            self.best_score = score
            self.patience_counter = 0
            print(f"✅ Nové nejlepší skóre: {score:.4f} (zlepšení o {improvement:.4f})")
        else:
            self.patience_counter += 1
            print(f"⏳ Žádné zlepšení ({self.patience_counter}/{self.patience}), skóre: {score:.4f} (nejlepší: {self.best_score:.4f})")
            if self.patience_counter >= self.patience:
                self.should_stop = True
                print(f"🛑 Early stopping aktivován! Nejlepší skóre: {self.best_score:.4f}")
        return metrics


# ============================================================================
# PARAMETRY PRO TRÉNINK - NASTAVTE PODLE POTŘEBY
# ============================================================================

# Cesty k datům a modelům
TRAINING_DATA_PATH = "training_data/training_set.xlsx"
OUTPUT_MODEL_DIR = "./fine_tuned_sbert"
BASE_MODEL_NAME = "sentence-transformers/paraphrase-multilingual-mpnet-base-v2"

# Parametry trénování
EPOCHS = 30
BATCH_SIZE = 32
WARMUP_STEPS = 100
TRAIN_RATIO = 0.8
EVALUATION_STEPS = 5
LEARNING_RATE = 2e-5  # stabilnější
EARLY_STOP_PATIENCE = 5
MIN_DELTA = 0.005


# Zařízení pro trénování
DEVICE = "cpu"  # Změňte na "cuda" nebo "mps" podle potřeby

# Evaluace po trénování
RUN_EVALUATION = True
TEST_DATA_PATH = None  # Volitelné - cesta k testovacím datům

def load_training_data(xlsx_path):
    """
    Načte tréninková data z Excel souboru pomocí generate_smart_triplet_dataset.

    Args:
        xlsx_path: cesta k Excel souboru s tréninkovými daty

    Returns:
        DataFrame s triplet daty
    """
    print(f"Načítám tréninková data z: {xlsx_path}")

    if not os.path.exists(xlsx_path):
        raise FileNotFoundError(f"Soubor nenalezen: {xlsx_path}")

    # Načteme strukturovaná data
    class_info, training_data = load_training_data_from_xlsx(xlsx_path)

    # Použijeme optimalizovaný adaptivní generátor s vylepšeným early stopping
    triplet_list = generate_adaptive_triplet_dataset(
        training_data,
        model_dir='fine_tuned_sbert',
        target_pos_threshold=0.80,
        target_neg_threshold=0.65,
        max_iterations=8,
        pairs_per_iteration=5,
        verbose=True,
        reset_model=False  # Pokračuj s existujícím modelem
    )

    # Převedeme na DataFrame
    triplet_data = pd.DataFrame(triplet_list)

    print(f"✅ Načteno {len(triplet_data)} tripletů")
    print(f"📊 Sloupce: {list(triplet_data.columns)}")

    # Zobrazíme ukázku dat
    print("\n📝 Ukázka dat:")
    for i in range(min(3, len(triplet_data))):
        row = triplet_data.iloc[i]
        print(f"  {i+1}. Anchor: '{row['anchor'][:50]}...'")
        print(f"     Positive: '{row['positive']}'")
        print(f"     Negative: '{row['negative']}'")
        print()

    # Uložíme poslední margin pro použití v tréninku
    if triplet_list:
        from semantic_classifier.data_util import get_adaptive_params
        final_params = get_adaptive_params(7, 8)  # poslední iterace
        triplet_data.attrs['final_margin'] = final_params['triplet_margin']
    else:
        triplet_data.attrs['final_margin'] = 0.6

    return triplet_data

def prepare_sentence_transformer(base_model_name, device):
    """
    Připraví SentenceTransformer model pro fine-tuning.

    Args:
        base_model_name: název základního modelu
        device: zařízení pro trénování

    Returns:
        SentenceTransformer model
    """
    word_embedding_model = models.Transformer(base_model_name)
    pooling_model = models.Pooling(
        word_embedding_model.get_word_embedding_dimension(),
        pooling_mode_mean_tokens=True
    )
    normalize_layer = models.Normalize()  # KRITICKÉ pro kosinusovou podobnost

    model = SentenceTransformer(
        modules=[word_embedding_model, pooling_model, normalize_layer],
        device=device
    )

    return model

def prepare_evaluator(examples, name='val', patience=5, min_delta=0.001):
    """
    Připraví BinaryClassificationEvaluator s early stopping.

    Pro každý triplet vytvoříme dva páry:
    - (anchor, positive) s labelem 1.0
    - (anchor, negative) s labelem 0.0
    """
    sentences1 = []
    sentences2 = []
    labels = []

    for ex in examples:
        sentences1.append(ex.texts[0]); sentences2.append(ex.texts[1]); labels.append(1.0)
        sentences1.append(ex.texts[0]); sentences2.append(ex.texts[2]); labels.append(0.0)

    return EarlyStoppingBinaryEvaluator(
        sentences1, sentences2, labels,
        name=name,
        patience=patience,
        min_delta=min_delta
    )


def prepare_training_data(triplet_data, train_ratio=0.8):
    """
    Připraví tréninková a validační data z triplet datasetu.

    Args:
        triplet_data: DataFrame s triplet daty
        train_ratio: poměr trénovacích dat (0.8 = 80% train, 20% validation)

    Returns:
        tuple: (train_examples, val_examples)
    """
    print(f"Připravuji tréninková data (train ratio: {train_ratio})")

    # Rozdělíme data na train/validation
    n_total = len(triplet_data)
    n_train = int(n_total * train_ratio)

    # Zamícháme data
    shuffled_data = triplet_data.sample(frac=1, random_state=42).reset_index(drop=True)

    train_data = shuffled_data[:n_train]
    val_data = shuffled_data[n_train:]

    # Vytvoříme InputExample objekty pro trénování
    train_examples = []
    for _, row in train_data.iterrows():
        example = InputExample(
            texts=[row['anchor'], row['positive'], row['negative']]
        )
        train_examples.append(example)

    # Vytvoříme InputExample objekty pro validaci
    val_examples = []
    for _, row in val_data.iterrows():
        example = InputExample(
            texts=[row['anchor'], row['positive'], row['negative']]
        )
        val_examples.append(example)

    print(f"✅ Připraveno {len(train_examples)} trénovacích a {len(val_examples)} validačních příkladů")

    return train_examples, val_examples

def fine_tune_model(triplet_data, base_model_name, device, output_dir,
                   epochs, batch_size, warmup_steps, train_ratio,
                   evaluation_steps, learning_rate, early_stop_patience, min_delta):
    """
    Provede fine-tuning sentence transformer modelu.

    Args:
        triplet_data: DataFrame s triplet daty
        base_model_name: název základního modelu
        device: zařízení pro trénování
        output_dir: složka pro uložení vyladěného modelu
        epochs: počet epoch
        batch_size: velikost batch
        warmup_steps: počet warmup kroků
        train_ratio: poměr trénovacích dat
        evaluation_steps: jak často evaluovat
        learning_rate: learning rate pro optimizátor
        early_stop_patience: počet evaluací bez zlepšení před zastavením
        min_delta: minimální zlepšení pro reset patience

    Returns:
        str: cesta k vyladěnému modelu
    """
    print("=" * 80)
    print("FINE-TUNING SENTENCE TRANSFORMER")
    print("=" * 80)

    # 1. Připravíme model
    model = prepare_sentence_transformer(base_model_name, device)

    # 2. Připravíme tréninková data
    train_examples, val_examples = prepare_training_data(triplet_data, train_ratio)

    # 3. Vytvoříme DataLoader
    train_dataloader = DataLoader(train_examples, shuffle=True, batch_size=batch_size, pin_memory=False)

    # 4. Definujeme loss funkci s kosínovou vzdáleností - použijeme adaptivní margin
    adaptive_margin = getattr(triplet_data, 'attrs', {}).get('final_margin', 0.6)
    print(f"🎯 Používám adaptivní triplet margin: {adaptive_margin:.3f}")

    train_loss = losses.TripletLoss(
        model=model,
        distance_metric=losses.TripletDistanceMetric.COSINE,
        triplet_margin=adaptive_margin
    )

    # 5. Vytvoření TripletEvaluatoru s early stopping
    val_evaluator = prepare_evaluator(val_examples, patience=early_stop_patience, min_delta=min_delta)

    # 6. Nastavíme parametry trénování
    total_steps = len(train_dataloader) * epochs

    print(f"\n📋 Parametry trénování:")
    print(f"  Epochs: {epochs}")
    print(f"  Batch size: {batch_size}")
    print(f"  Total steps: {total_steps}")
    print(f"  Warmup steps: {warmup_steps}")
    print(f"  Evaluation steps: {evaluation_steps}")
    print(f"  Learning rate: {learning_rate}")
    print(f"  Early stop patience: {early_stop_patience}")
    print(f"  Min delta: {min_delta}")
    print(f"  Output dir: {output_dir}")

    # 7. Spustíme fine-tuning s vlastní early stopping smyčkou
    print(f"\n🚀 Spouštím fine-tuning s early stopping...")
    print(f"ℹ️  Early stopping: aktivní po {early_stop_patience} evaluacích bez zlepšení")

    # Vlastní tréninkova smyčka s early stopping
    steps_per_epoch = len(train_dataloader)

    try:
        for epoch in range(epochs):
            print(f"\n📈 Epoch {epoch + 1}/{epochs}")

            # Trénujeme jednu epochu bez evaluace
            model.fit(
                train_objectives=[(train_dataloader, train_loss)],
                epochs=10,  # Jen jedna epocha
                evaluation_steps=0,  # Bez evaluace během trénování
                warmup_steps=warmup_steps if epoch == 0 else 0,
                output_path=output_dir,
                optimizer_params={'lr': learning_rate, 'eps': 1e-8, 'betas': (0.9, 0.999)},
                weight_decay=0.01,
                scheduler='warmupLinear' if epoch == 0 else 'constantlr',
                show_progress_bar=True,
                save_best_model=False,  # Uložíme manuálně
                use_amp=False,
            )

            # Evaluace po každé epoše
            print(f"🔍 Evaluace po epoše {epoch + 1}...")
            metrics = val_evaluator(model, output_dir, epoch=epoch + 1, steps=(epoch + 1) * steps_per_epoch)

            # Uložíme decision threshold z evaluátoru (pokud je k dispozici)
            try:
                thr = None
                if isinstance(metrics, dict):
                    thr = metrics.get('val_cosine_accuracy_threshold') or metrics.get('cosine_accuracy_threshold')
                if thr is not None:
                    with open(os.path.join(output_dir, 'best_threshold.json'), 'w', encoding='utf-8') as f:
                        json.dump({'threshold': float(thr)}, f)
                    print(f"📏 Uložen decision threshold z evaluátoru: {thr:.4f}")
            except Exception:
                pass

            # Uložíme model pokud je nejlepší
            if val_evaluator.best_score > 0 and not val_evaluator.should_stop:
                model.save(output_dir)
                print(f"💾 Model uložen s skóre {val_evaluator.best_score:.4f}")

            # Kontrola early stopping
            if val_evaluator.should_stop:
                print(f"\n🛑 Early stopping aktivován po {epoch + 1} epochách!")
                print(f"📊 Nejlepší dosažené skóre: {val_evaluator.best_score:.4f}")
                break
        else:
            print(f"\n✅ Trénink dokončen po všech {epochs} epochách")
            print(f"📊 Finální nejlepší skóre: {val_evaluator.best_score:.4f}")

            # Uložíme decision threshold z poslední evaluace (pokud je k dispozici)
            try:
                metrics = val_evaluator(model, output_dir, epoch=epoch + 1, steps=(epoch + 1) * steps_per_epoch)
                thr = None
                if isinstance(metrics, dict):
                    thr = metrics.get('val_cosine_accuracy_threshold') or metrics.get('cosine_accuracy_threshold')
                if thr is not None:
                    with open(os.path.join(output_dir, 'best_threshold.json'), 'w', encoding='utf-8') as f:
                        json.dump({'threshold': float(thr)}, f)
                    print(f"📏 Uložen decision threshold z evaluátoru: {thr:.4f}")
            except Exception:
                pass


    except KeyboardInterrupt:
        print(f"\n⚠️ Trénink přerušen uživatelem")
        print(f"📊 Nejlepší dosažené skóre: {val_evaluator.best_score:.4f}")

    print(f"\n✅ Fine-tuning dokončen!")
    print(f"📁 Vyladěný model uložen v: {output_dir}")

    return output_dir

def evaluate_model(model_path, base_model_name, device, test_data_path=None):
    """
    Evaluuje vyladěný model a porovná ho s původním.

    Args:
        model_path: cesta k vyladěnému modelu
        base_model_name: název původního modelu
        device: zařízení pro evaluaci
        test_data_path: cesta k testovacím datům (volitelné)
    """
    print(f"\n📊 Evaluace modelu: {model_path}")

    # Načteme vyladěný model
    tuned_model = SentenceTransformer(model_path, device=device)

    # Pokud máme testovací data, použijeme je
    if test_data_path and os.path.exists(test_data_path):
        test_data = generate_smart_triplet_dataset(test_data_path)
        print(f"Testovací data: {len(test_data)} tripletů")

        # Vytvoříme test examples
        test_examples = []
        for _, row in test_data.iterrows():
            example = InputExample(
                texts=[row['anchor'], row['positive'], row['negative']]
            )
            test_examples.append(example)

        # Evaluace
        evaluator = TripletEvaluator.from_input_examples(
            test_examples,
            name='test'
        )

        score = evaluator(tuned_model)
        print(f"📈 Test score: {score}")

    else:
        print("ℹ️  Žádná testovací data k dispozici")

    # Porovnání s původním modelem
    print(f"\n🔄 Porovnání s původním modelem...")
    original_model = SentenceTransformer(base_model_name, device=device)

    # Test na několika příkladech
    test_texts = [
        "číslo faktury",
        "datum vystavení",
        "celková částka",
        "k úhradě",
        "dodavatel",
        "IČO",
        "DIČ"
    ]

    print("Porovnání embeddingů:")
    for text in test_texts:
        orig_emb = original_model.encode([text])[0]
        tuned_emb = tuned_model.encode([text])[0]

        # Kosinusová podobnost
        similarity = np.dot(orig_emb, tuned_emb) / (np.linalg.norm(orig_emb) * np.linalg.norm(tuned_emb))

        print(f"  '{text}': podobnost {similarity:.3f}")


def test_with_sbert_classifier(model_path):
    """
    Testuje vyladěný model pomocí SBertClassifier třídy.

    Args:
        model_path: cesta k vyladěnému modelu
    """
    print(f"\n🧪 Test s SBertClassifier...")

    # Vytvoříme SBertClassifier s vyladěným modelem
    classifier = SBertClassifier(
        model_name=model_path,
        model_dir=model_path,
        device=DEVICE
    )

    # Test podobnosti
    test_pairs = [
        ("číslo faktury", "invoice number"),
        ("datum vystavení", "issue date"),
        ("celková částka", "total amount"),
        ("dodavatel", "supplier")
    ]

    print("Test podobnosti s vyladěným modelem:")
    for text1, text2 in test_pairs:
        similarity = classifier.similarity(text1, text2)
        print(f"  '{text1}' <-> '{text2}': {similarity:.3f}")

    return classifier

def main():
    """
    Hlavní funkce pro spuštění fine-tuningu.
    Všechny parametry jsou nastaveny pomocí proměnných na začátku souboru.
    """
    print("=" * 80)
    print("FINE-TUNING SENTENCE TRANSFORMER - IDE VERZE")
    print("=" * 80)
    print(f"📁 Tréninková data: {TRAINING_DATA_PATH}")
    print(f"📁 Výstupní model: {OUTPUT_MODEL_DIR}")
    print(f"🖥️  Zařízení: {DEVICE}")
    print("=" * 80)

    try:
        # 1. Načteme tréninková data a provedeme iterativní trénink
        triplet_data = load_training_data(TRAINING_DATA_PATH)

        # Model byl již natrénován iterativně v load_training_data
        output_dir = OUTPUT_MODEL_DIR
        print(f"\n🎯 Iterativní trénink dokončen!")
        print(f"📁 Model uložen v: {output_dir}")

        # 3. Evaluace (pokud je povolena)
        if RUN_EVALUATION:
            evaluate_model(
                model_path=output_dir,
                base_model_name=BASE_MODEL_NAME,
                device=DEVICE,
                test_data_path=TEST_DATA_PATH
            )

            # 4. Test s SBertClassifier
            classifier = test_with_sbert_classifier(output_dir)

        print(f"\n✅ Vše dokončeno!")
        print(f"📁 Vyladěný model je k dispozici v: {output_dir}")
        print(f"💡 Pro použití s SBertClassifier:")
        print(f"   classifier = SBertClassifier(model_name='{output_dir}', model_dir='{output_dir}')")

    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
