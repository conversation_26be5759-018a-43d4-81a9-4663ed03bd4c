"""
Implemetace je finální. Změny jsou možné pouze po předchozím dotazu.
"""

from pathlib import Path
from SBertClassifier import SBertClassifier
from generate_triplets import generate_triplets
from load_training_data_with_scores_xlsx import load_training_data_with_scores_xlsx
from DataClass import Class

# Inicializace klasifikátoru 
classifier = SBertClassifier()

# Cesta k testovacímu XLSX souboru
data_path = Path("training_data/training_set.xlsx")  # nebo cesta k adresáři s souborem

if not data_path.exists():
    print(f"Soubor {data_path} neexistuje, ukončuji test.")
    exit(1)

# Načtení a scoring dat
classes:Class = load_training_data_with_scores_xlsx(str(data_path), classifier)

# Výpis výsledků
for category_name, cls in classes.items():
    print(f"\n=== Kategorie: {category_name} ===")
    print(f"Kotva: {cls.anchor}")
    print("Pozitivní příklady a skóre:")
    for text, score in cls.positive:
        print(f"  {score:.3f} -> {text}")
    print("Negativní příklady a skóre:")
    for text, score in cls.negative:
        print(f"  {score:.3f} -> {text}")

triplets = generate_triplets(classes)

# Výpis výsledků
print("=== Vygenerované triplety ===")
for anchor, pos, neg, strength in triplets:
    print(f"Anchor: {anchor:10s} | Positive: {pos:10s} | Negative: {neg:10s} | Strength: {strength:.3f}")
